/*
 * Copyright (c) 2024 VPN Service V2
 * VpnService-based OpenVPN implementation without native binary execution
 * Compatible with Android 10+ and SELinux restrictions
 */

package de.blinkt.openvpn.core;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.net.VpnService;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.ParcelFileDescriptor;
import android.util.Log;

import androidx.annotation.RequiresApi;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.ByteBuffer;
import java.nio.channels.DatagramChannel;
import java.util.concurrent.atomic.AtomicBoolean;

import de.blinkt.openvpn.R;
import de.blinkt.openvpn.VpnProfile;

/**
 * VpnService-based OpenVPN implementation that doesn't rely on native binary execution.
 * This implementation uses Android's VpnService API directly to establish VPN connections,
 * making it compatible with Android 10+ and avoiding SELinux permission issues.
 */
public class OpenVPNServiceV2 extends VpnService {
    
    private static final String TAG = "OpenVPNServiceV2";
    private static final String NOTIFICATION_CHANNEL_ID = "openvpn_v2_channel";
    private static final int NOTIFICATION_ID = 1001;
    
    // Service state
    private final AtomicBoolean isConnected = new AtomicBoolean(false);
    private final AtomicBoolean isConnecting = new AtomicBoolean(false);
    private ParcelFileDescriptor vpnInterface;
    private Thread vpnThread;
    private VpnProfile currentProfile;
    private String currentConfig;
    private String currentUsername;
    private String currentPassword;
    private Handler mainHandler;
    private SimpleOpenVPNClient openVpnClient;
    
    // Binder for local service binding
    private final IBinder binder = new LocalBinder();
    
    public class LocalBinder extends Binder {
        public OpenVPNServiceV2 getService() {
            return OpenVPNServiceV2.this;
        }
    }
    
    @Override
    public void onCreate() {
        super.onCreate();
        mainHandler = new Handler(getMainLooper());
        createNotificationChannel();
        openVpnClient = new SimpleOpenVPNClient(this);
        Log.d(TAG, "OpenVPNServiceV2 created");
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String action = intent.getAction();
            if ("CONNECT".equals(action)) {
                VpnProfile profile = intent.getParcelableExtra("profile");
                String config = intent.getStringExtra("config");
                String username = intent.getStringExtra("username");
                String password = intent.getStringExtra("password");

                if (profile != null || config != null) {
                    startVpnConnection(profile, config, username, password);
                }
            } else if ("DISCONNECT".equals(action)) {
                stopVpnConnection();
            }
        }
        return START_STICKY;
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return binder;
    }
    
    @Override
    public void onDestroy() {
        stopVpnConnection();
        super.onDestroy();
        Log.d(TAG, "OpenVPNServiceV2 destroyed");
    }
    
    @Override
    public void onRevoke() {
        Log.w(TAG, "VPN permission revoked");
        stopVpnConnection();
        super.onRevoke();
    }
    
    /**
     * Start VPN connection with the given profile or configuration
     */
    public void startVpnConnection(VpnProfile profile, String config, String username, String password) {
        if (isConnecting.get() || isConnected.get()) {
            Log.w(TAG, "VPN already connecting or connected");
            return;
        }

        currentProfile = profile;
        currentConfig = config;
        currentUsername = username;
        currentPassword = password;
        isConnecting.set(true);

        // Show connecting notification
        showNotification("Connecting...", false);
        broadcastState("CONNECTING");

        // Start VPN connection in background thread
        vpnThread = new Thread(this::establishVpnConnection, "VPN-Connection-Thread");
        vpnThread.start();
    }
    
    /**
     * Stop VPN connection
     */
    public void stopVpnConnection() {
        Log.d(TAG, "Stopping VPN connection");

        isConnecting.set(false);
        isConnected.set(false);

        // Stop OpenVPN client
        if (openVpnClient != null) {
            openVpnClient.disconnect();
        }

        // Interrupt VPN thread
        if (vpnThread != null && vpnThread.isAlive()) {
            vpnThread.interrupt();
        }

        // Close VPN interface
        if (vpnInterface != null) {
            try {
                vpnInterface.close();
            } catch (IOException e) {
                Log.e(TAG, "Error closing VPN interface", e);
            }
            vpnInterface = null;
        }

        // Update UI
        broadcastState("DISCONNECTED");
        stopForeground(true);
        stopSelf();
    }
    
    /**
     * Establish VPN connection using VpnService API
     */
    private void establishVpnConnection() {
        try {
            Log.d(TAG, "Establishing VPN connection");

            // Get server configuration
            String serverConfig;
            if (currentConfig != null) {
                // Use provided configuration string
                serverConfig = currentConfig;
            } else if (currentProfile != null) {
                // Generate configuration from profile
                serverConfig = currentProfile.getConfigFile(this, false);
            } else {
                throw new Exception("No configuration available");
            }

            VpnConnectionConfig config = parseOpenVpnConfig(serverConfig);

            if (config == null) {
                throw new Exception("Failed to parse OpenVPN configuration");
            }
            
            // Create VPN interface
            vpnInterface = createVpnInterface(config);
            if (vpnInterface == null) {
                throw new Exception("Failed to create VPN interface");
            }
            
            // Configure and start OpenVPN client
            openVpnClient.configure(config.serverHost, config.serverPort, config.username, config.password);

            if (!openVpnClient.connect(vpnInterface)) {
                throw new Exception("Failed to start OpenVPN client");
            }

            // Mark as connected
            isConnecting.set(false);
            isConnected.set(true);

            // Update UI
            mainHandler.post(() -> {
                showNotification("Connected", true);
                broadcastState("CONNECTED");
            });

            Log.d(TAG, "VPN connection established successfully");
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to establish VPN connection", e);
            
            isConnecting.set(false);
            isConnected.set(false);
            
            mainHandler.post(() -> {
                broadcastState("DISCONNECTED");
                stopSelf();
            });
        }
    }
    
    /**
     * Parse OpenVPN configuration
     */
    private VpnConnectionConfig parseOpenVpnConfig(String config) {
        try {
            VpnConnectionConfig vpnConfig = new VpnConnectionConfig();

            // Parse basic configuration from OpenVPN config
            String[] lines = config.split("\n");

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith("#")) continue;

                String[] parts = line.split("\\s+");
                if (parts.length < 2) continue;

                String directive = parts[0].toLowerCase();

                switch (directive) {
                    case "remote":
                        vpnConfig.serverHost = parts[1];
                        if (parts.length > 2) {
                            try {
                                vpnConfig.serverPort = Integer.parseInt(parts[2]);
                            } catch (NumberFormatException e) {
                                vpnConfig.serverPort = 1194; // Default OpenVPN port
                            }
                        }
                        if (parts.length > 3) {
                            vpnConfig.protocol = parts[3].toLowerCase();
                        } else {
                            vpnConfig.protocol = "udp"; // Default protocol
                        }
                        break;

                    case "proto":
                        vpnConfig.protocol = parts[1].toLowerCase();
                        break;

                    case "port":
                        try {
                            vpnConfig.serverPort = Integer.parseInt(parts[1]);
                        } catch (NumberFormatException e) {
                            vpnConfig.serverPort = 1194;
                        }
                        break;
                }
            }

            // Set defaults if not specified
            if (vpnConfig.serverHost == null) {
                Log.e(TAG, "No remote server specified in config");
                return null;
            }

            if (vpnConfig.serverPort == 0) {
                vpnConfig.serverPort = 1194;
            }

            if (vpnConfig.protocol == null) {
                vpnConfig.protocol = "udp";
            }

            // Set default VPN network configuration
            vpnConfig.localIP = "********";
            vpnConfig.netmask = "*************";
            vpnConfig.vpnGateway = "********";
            vpnConfig.dnsServers = new String[]{"*******", "*******"};
            vpnConfig.routes = new String[]{"0.0.0.0/0"};

            // Set authentication from provided credentials or profile
            if (currentUsername != null && currentPassword != null) {
                vpnConfig.username = currentUsername;
                vpnConfig.password = currentPassword;
            } else if (currentProfile != null) {
                vpnConfig.username = currentProfile.mUsername;
                vpnConfig.password = currentProfile.mPassword;
            }

            Log.d(TAG, "Parsed config - Server: " + vpnConfig.serverHost + ":" + vpnConfig.serverPort + " (" + vpnConfig.protocol + ")");
            return vpnConfig;

        } catch (Exception e) {
            Log.e(TAG, "Error parsing OpenVPN config", e);
            return null;
        }
    }

    /**
     * Create VPN interface using VpnService.Builder
     */
    private ParcelFileDescriptor createVpnInterface(VpnConnectionConfig config) {
        try {
            Builder builder = new Builder();

            // Set VPN session name
            builder.setSession("OpenVPN V2");

            // Configure IP addresses
            builder.addAddress(config.localIP, 24);

            // Add DNS servers
            for (String dns : config.dnsServers) {
                builder.addDnsServer(dns);
            }

            // Add routes
            for (String route : config.routes) {
                if (route.contains("/")) {
                    String[] routeParts = route.split("/");
                    if (routeParts.length == 2) {
                        try {
                            int prefixLength = Integer.parseInt(routeParts[1]);
                            builder.addRoute(routeParts[0], prefixLength);
                        } catch (NumberFormatException e) {
                            Log.w(TAG, "Invalid route format: " + route);
                        }
                    }
                } else {
                    // Assume /32 for single IP
                    builder.addRoute(route, 32);
                }
            }

            // Set MTU
            builder.setMtu(1500);

            // Allow all applications to use VPN by default
            // You can modify this to exclude specific apps if needed

            // Establish the VPN interface
            return builder.establish();

        } catch (Exception e) {
            Log.e(TAG, "Error creating VPN interface", e);
            return null;
        }
    }


    
    /**
     * Create notification channel for Android O+
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                NOTIFICATION_CHANNEL_ID,
                "VPN Service",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("VPN connection status");
            channel.setLightColor(Color.BLUE);
            channel.setLockscreenVisibility(Notification.VISIBILITY_PRIVATE);
            
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }
    
    /**
     * Show notification
     */
    private void showNotification(String message, boolean isConnected) {
        try {
            Intent notificationIntent = new Intent(this, getClass());
            PendingIntent pendingIntent;

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getActivity(this, 0, notificationIntent, PendingIntent.FLAG_MUTABLE);
            } else {
                pendingIntent = PendingIntent.getActivity(this, 0, notificationIntent, 0);
            }

            Notification.Builder builder = new Notification.Builder(this)
                .setContentTitle("VPN Service V2")
                .setContentText(message)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentIntent(pendingIntent)
                .setOngoing(isConnected);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                builder.setChannelId(NOTIFICATION_CHANNEL_ID);
            }

            // Add disconnect action if connected
            if (isConnected) {
                Intent disconnectIntent = new Intent(this, OpenVPNServiceV2.class);
                disconnectIntent.setAction("DISCONNECT");

                PendingIntent disconnectPendingIntent;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    disconnectPendingIntent = PendingIntent.getService(this, 0, disconnectIntent, PendingIntent.FLAG_MUTABLE);
                } else {
                    disconnectPendingIntent = PendingIntent.getService(this, 0, disconnectIntent, 0);
                }

                builder.addAction(R.drawable.ic_menu_close_clear_cancel, "Disconnect", disconnectPendingIntent);
            }

            Notification notification = builder.build();
            startForeground(NOTIFICATION_ID, notification);

        } catch (Exception e) {
            Log.e(TAG, "Error showing notification", e);
        }
    }
    
    /**
     * Broadcast connection state to UI
     */
    private void broadcastState(String state) {
        Intent intent = new Intent("connectionState");
        intent.putExtra("state", state);
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
    }
    
    /**
     * Check if VPN is connected
     */
    public boolean isConnected() {
        return isConnected.get();
    }
    
    /**
     * Check if VPN is connecting
     */
    public boolean isConnecting() {
        return isConnecting.get();
    }
    
    /**
     * Configuration class for VPN connection
     */
    private static class VpnConnectionConfig {
        String serverHost;
        int serverPort;
        String protocol; // udp or tcp
        String vpnGateway;
        String localIP;
        String netmask;
        String[] dnsServers;
        String[] routes;
        
        // Authentication
        String username;
        String password;
        String caCert;
        String clientCert;
        String clientKey;
    }
}
